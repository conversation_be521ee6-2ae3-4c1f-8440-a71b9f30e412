<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SEO Analysis Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
    }
    .navbar-brand {
      font-weight: 700;
    }
    .card {
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border: none;
    }
    .card-header {
      background-color: #fff;
      border-bottom: 1px solid #eee;
      font-weight: 600;
      padding: 15px 20px;
    }
    .form-label {
      font-weight: 500;
    }
    .progress {
      height: 10px;
      border-radius: 5px;
    }
    #taskStatus {
      transition: all 0.3s ease;
    }
    .status-card {
      transition: all 0.3s ease;
    }
    .hidden {
      display: none;
    }
    .btn-primary {
      background-color: #4361ee;
      border-color: #4361ee;
    }
    .btn-primary:hover {
      background-color: #3a56d4;
      border-color: #3a56d4;
    }
    .btn-outline-primary {
      color: #4361ee;
      border-color: #4361ee;
    }
    .btn-outline-primary:hover {
      background-color: #4361ee;
      border-color: #4361ee;
    }
    .alert-info {
      background-color: #e6f3ff;
      border-color: #b8daff;
      color: #0c63e4;
    }
    .text-primary {
      color: #4361ee !important;
    }
    .bg-primary {
      background-color: #4361ee !important;
    }
    .file-upload {
      position: relative;
      overflow: hidden;
      margin: 10px 0;
    }
    .file-upload input[type=file] {
      position: absolute;
      top: 0;
      right: 0;
      min-width: 100%;
      min-height: 100%;
      font-size: 100px;
      text-align: right;
      filter: alpha(opacity=0);
      opacity: 0;
      outline: none;
      background: white;
      cursor: pointer;
      display: block;
    }
    .file-upload-name {
      margin-top: 5px;
      font-size: 0.875rem;
      color: #6c757d;
    }
    #recentReports .list-group-item {
      border-left: none;
      border-right: none;
      padding: 15px 20px;
    }
    #recentReports .list-group-item:first-child {
      border-top: none;
    }
    .report-date {
      font-size: 0.8rem;
      color: #6c757d;
    }
    .domain-badge {
      font-size: 0.75rem;
      padding: 5px 10px;
      border-radius: 20px;
      background-color: #e9ecef;
      color: #495057;
      font-weight: 600;
    }
  </style>
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
      <a class="navbar-brand" href="#">
        <i class="bi bi-bar-chart-line me-2"></i>
        SEO Analysis Dashboard
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item">
            <a class="nav-link active" href="#"><i class="bi bi-house me-1"></i> Home</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-file-earmark-text me-1"></i> Reports</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#"><i class="bi bi-gear me-1"></i> Settings</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="container py-4">
    <div class="row">
      <div class="col-lg-8">
        <!-- Configuration Form -->
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <span><i class="bi bi-gear-fill me-2"></i>Analysis Configuration</span>
            <button class="btn btn-sm btn-outline-primary" id="loadConfigBtn">
              <i class="bi bi-upload me-1"></i>Load Config
            </button>
          </div>
          <div class="card-body">
            <form id="analysisForm">
              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="domainProperty" class="form-label">Domain Property</label>
                  <input type="text" class="form-control" id="domainProperty" placeholder="https://example.com/" required>
                  <div class="form-text">Your domain in Google Search Console</div>
                </div>
                <div class="col-md-6">
                  <label for="gaPropertyId" class="form-label">GA Property ID</label>
                  <input type="text" class="form-control" id="gaPropertyId" placeholder="123456789" required>
                  <div class="form-text">Your Google Analytics 4 property ID</div>
                </div>
              </div>

              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="homepage" class="form-label">Homepage URL</label>
                  <input type="text" class="form-control" id="homepage" placeholder="https://example.com/">
                  <div class="form-text">Optional, defaults to domain property</div>
                </div>
                <div class="col-md-6">
                  <label for="serviceAccountFile" class="form-label">Service Account JSON</label>
                  <div class="file-upload">
                    <button type="button" class="btn btn-outline-secondary w-100">
                      <i class="bi bi-file-earmark-text me-2"></i>Choose Service Account File
                    </button>
                    <input type="file" class="form-control" id="serviceAccountFile" accept=".json" required>
                  </div>
                  <div class="file-upload-name" id="fileUploadName">No file chosen</div>
                </div>
              </div>

              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="startDate" class="form-label">Start Date</label>
                  <input type="date" class="form-control" id="startDate">
                  <div class="form-text">Optional, defaults to 1 year ago</div>
                </div>
                <div class="col-md-6">
                  <label for="endDate" class="form-label">End Date</label>
                  <input type="date" class="form-control" id="endDate">
                  <div class="form-text">Optional, defaults to today</div>
                </div>
              </div>

              <div class="mb-3">
                <label for="websiteUrls" class="form-label">Website URLs (Optional)</label>
                <textarea class="form-control" id="websiteUrls" rows="3" placeholder="https://example.com/page1&#10;https://example.com/page2"></textarea>
                <div class="form-text">Enter one URL per line, or leave empty to auto-discover</div>
              </div>

              <div class="mb-3">
                <label for="wpApiKey" class="form-label">WordPress API Key (Optional)</label>
                <input type="text" class="form-control" id="wpApiKey" placeholder="your-wordpress-api-key">
                <div class="form-text">If your site uses WordPress with the Data Exporter plugin</div>
              </div>

              <div class="row mb-3">
                <div class="col-md-6">
                  <label for="supabaseUrl" class="form-label">Supabase URL <span class="text-danger">*</span></label>
                  <input type="url" class="form-control" id="supabaseUrl" placeholder="https://your-project-id.supabase.co" required>
                  <small class="form-text text-muted">Required: All analysis data will be stored in Supabase</small>
                </div>
                <div class="col-md-6">
                  <label for="supabaseKey" class="form-label">Supabase API Key <span class="text-danger">*</span></label>
                  <input type="password" class="form-control" id="supabaseKey" placeholder="your-supabase-api-key" required>
                  <small class="form-text text-muted">Required: API key for Supabase database access</small>
                </div>
              </div>

              <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary" id="submitBtn">
                  <i class="bi bi-play-fill me-2"></i>Start Analysis
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Task Status Card (Hidden initially) -->
        <div class="card status-card hidden" id="taskStatus">
          <div class="card-header d-flex justify-content-between align-items-center">
            <span><i class="bi bi-activity me-2"></i>Analysis Progress</span>
            <span class="badge bg-primary" id="statusBadge">Running</span>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <div class="d-flex justify-content-between mb-1">
                <span>Progress:</span>
                <span id="progressPercentage">0%</span>
              </div>
              <div class="progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated" id="progressBar" role="progressbar" style="width: 0%"></div>
              </div>
            </div>
            <div class="mb-3">
              <p class="mb-1 fw-bold">Current Task:</p>
              <p id="currentTask">Initializing...</p>
            </div>
            <div class="alert alert-info" role="alert">
              <i class="bi bi-info-circle me-2"></i>
              <span id="taskMessage">Analysis is running. This may take several minutes depending on the size of your site.</span>
            </div>
            <div class="d-flex justify-content-between">
              <button class="btn btn-outline-secondary" id="cancelBtn">
                <i class="bi bi-x-circle me-1"></i>Cancel
              </button>
              <a href="#" class="btn btn-success hidden" id="downloadBtn">
                <i class="bi bi-download me-1"></i>Download Report
              </a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-4">
        <!-- Recent Reports -->
        <div class="card">
          <div class="card-header">
            <i class="bi bi-clock-history me-2"></i>Recent Reports
          </div>
          <div class="list-group list-group-flush" id="recentReports">
            <div class="list-group-item text-center py-4">
              <i class="bi bi-folder2-open text-muted mb-2" style="font-size: 2rem;"></i>
              <p class="mb-0 text-muted">No recent reports</p>
            </div>
          </div>
        </div>

        <!-- Help Card -->
        <div class="card">
          <div class="card-header">
            <i class="bi bi-question-circle me-2"></i>Help & Resources
          </div>
          <div class="card-body">
            <h5 class="card-title">Getting Started</h5>
            <p class="card-text">To run an SEO analysis, you'll need:</p>
            <ul>
              <li>Google Search Console access</li>
              <li>Google Analytics 4 property</li>
              <li>Google service account JSON file</li>
            </ul>
            <h5 class="card-title mt-3">Service Account Setup</h5>
            <p class="card-text">Follow these steps to create a service account:</p>
            <ol>
              <li>Go to Google Cloud Console</li>
              <li>Create a new project</li>
              <li>Enable Search Console and Analytics APIs</li>
              <li>Create a service account with appropriate permissions</li>
              <li>Download the JSON credentials file</li>
            </ol>
            <a href="#" class="btn btn-outline-primary mt-2">
              <i class="bi bi-book me-1"></i>View Documentation
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="bg-light py-4 mt-4">
    <div class="container text-center">
      <p class="mb-0 text-muted">SEO Analysis Dashboard &copy; 2023</p>
    </div>
  </footer>

  <!-- Bootstrap & Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // API endpoint
      const API_ENDPOINT = 'http://localhost:8000';
      
      // DOM elements
      const analysisForm = document.getElementById('analysisForm');
      const taskStatus = document.getElementById('taskStatus');
      const progressBar = document.getElementById('progressBar');
      const progressPercentage = document.getElementById('progressPercentage');
      const currentTask = document.getElementById('currentTask');
      const statusBadge = document.getElementById('statusBadge');
      const taskMessage = document.getElementById('taskMessage');
      const downloadBtn = document.getElementById('downloadBtn');
      const cancelBtn = document.getElementById('cancelBtn');
      // Supabase fields are now always visible
      const serviceAccountFile = document.getElementById('serviceAccountFile');
      const fileUploadName = document.getElementById('fileUploadName');
      const loadConfigBtn = document.getElementById('loadConfigBtn');
      const recentReports = document.getElementById('recentReports');
      
      // Event listeners
      
      serviceAccountFile.addEventListener('change', function() {
        if (this.files.length > 0) {
          fileUploadName.textContent = this.files[0].name;
        } else {
          fileUploadName.textContent = 'No file chosen';
        }
      });
      
      loadConfigBtn.addEventListener('click', function() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = e => {
          const file = e.target.files[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = function(event) {
              try {
                const config = JSON.parse(event.target.result);
                populateForm(config);
              } catch (error) {
                alert('Invalid JSON file');
              }
            };
            reader.readAsText(file);
          }
        };
        input.click();
      });
      
      // Form submission
      analysisForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Get service account file
        const file = serviceAccountFile.files[0];
        if (!file) {
          alert('Please select a service account JSON file');
          return;
        }
        
        // Read file as text
        const fileReader = new FileReader();
        fileReader.onload = async function(event) {
          try {
            // Parse service account JSON
            const serviceAccount = JSON.parse(event.target.result);
            
            // Prepare form data
            const formData = {
              domain_property: document.getElementById('domainProperty').value,
              ga_property_id: document.getElementById('gaPropertyId').value,
              service_account_file: 'temp_service_account.json', // This will be handled server-side
              homepage: document.getElementById('homepage').value || null,
              start_date: document.getElementById('startDate').value || null,
              end_date: document.getElementById('endDate').value || null,
              wp_api_key: document.getElementById('wpApiKey').value || null,
              generate_excel: true
            };
            
            // Add website URLs if provided
            const websiteUrls = document.getElementById('websiteUrls').value.trim();
            if (websiteUrls) {
              formData.website_urls = websiteUrls.split('\n').map(url => url.trim()).filter(url => url);
            }
            
            // Add Supabase details (required)
            formData.supabase_url = document.getElementById('supabaseUrl').value;
            formData.supabase_key = document.getElementById('supabaseKey').value;
            
            // Show task status card
            taskStatus.classList.remove('hidden');
            
            // Submit analysis request
            const response = await fetch(`${API_ENDPOINT}/generate_report_with_service_account/`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                ...formData,
                service_account_data: serviceAccount // Send the parsed service account data
              })
            });
            
            if (!response.ok) {
              throw new Error(`API error: ${response.status}`);
            }
            
            const data = await response.json();
            const taskId = data.task_id;
            
            // Start polling for task status
            pollTaskStatus(taskId);
            
          } catch (error) {
            console.error('Error:', error);
            alert(`Error: ${error.message}`);
            taskStatus.classList.add('hidden');
          }
        };
        
        fileReader.readAsText(file);
      });
      
      // Cancel button
      cancelBtn.addEventListener('click', function() {
        // In a real implementation, you would call an API endpoint to cancel the task
        if (confirm('Are you sure you want to cancel the analysis?')) {
          taskStatus.classList.add('hidden');
          // You would also need to call an API endpoint to cancel the task
        }
      });
      
      // Function to poll task status
      async function pollTaskStatus(taskId) {
        try {
          const response = await fetch(`${API_ENDPOINT}/task/${taskId}`);
          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }
          
          const data = await response.json();
          
          // Update progress
          const progress = data.progress || 0;
          progressBar.style.width = `${progress}%`;
          progressPercentage.textContent = `${progress}%`;
          
          // Update status message
          currentTask.textContent = data.message || 'Processing...';
          
          // Update status badge
          statusBadge.textContent = data.status;
          if (data.status === 'completed') {
            statusBadge.classList.remove('bg-primary');
            statusBadge.classList.add('bg-success');
            taskMessage.textContent = 'Analysis completed successfully!';
            
            // Show download button
            if (data.result && data.result.excel_report) {
              downloadBtn.href = `${API_ENDPOINT}/download/${encodeURIComponent(data.result.excel_report)}`;
              downloadBtn.classList.remove('hidden');
            }
            
            // Add to recent reports
            addRecentReport(data.result);
            
            // Stop polling
            return;
          } else if (data.status === 'failed') {
            statusBadge.classList.remove('bg-primary');
            statusBadge.classList.add('bg-danger');
            taskMessage.textContent = `Analysis failed: ${data.error || 'Unknown error'}`;
            
            // Stop polling
            return;
          }
          
          // Continue polling
          setTimeout(() => pollTaskStatus(taskId), 2000);
          
        } catch (error) {
          console.error('Error polling task status:', error);
          taskMessage.textContent = `Error checking status: ${error.message}`;
          statusBadge.textContent = 'Error';
          statusBadge.classList.remove('bg-primary');
          statusBadge.classList.add('bg-danger');
        }
      }
      
      // Function to populate form from config
      function populateForm(config) {
        if (config.domain_property) document.getElementById('domainProperty').value = config.domain_property;
        if (config.ga_property_id) document.getElementById('gaPropertyId').value = config.ga_property_id;
        if (config.homepage) document.getElementById('homepage').value = config.homepage;
        if (config.start_date) document.getElementById('startDate').value = config.start_date;
        if (config.end_date) document.getElementById('endDate').value = config.end_date;
        if (config.wp_api_key) document.getElementById('wpApiKey').value = config.wp_api_key;
        
        if (config.website_urls && Array.isArray(config.website_urls)) {
          document.getElementById('websiteUrls').value = config.website_urls.join('\n');
        }
        
        if (config.supabase_url && config.supabase_key) {
          document.getElementById('supabaseUrl').value = config.supabase_url;
          document.getElementById('supabaseKey').value = config.supabase_key;
        }
      }
      
      // Function to add a recent report to the list
      function addRecentReport(result) {
        if (!result || !result.domain) return;
        
        // Clear "no reports" message if present
        if (recentReports.querySelector('.text-muted')) {
          recentReports.innerHTML = '';
        }
        
        // Create report item
        const reportItem = document.createElement('div');
        reportItem.className = 'list-group-item';
        
        const now = new Date();
        const dateStr = now.toLocaleDateString() + ' ' + now.toLocaleTimeString();
        
        reportItem.innerHTML = `
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <div class="mb-1">
                <span class="domain-badge">${result.domain}</span>
              </div>
              <div class="report-date">${dateStr}</div>
            </div>
            <a href="${API_ENDPOINT}/download/${encodeURIComponent(result.excel_report)}" class="btn btn-sm btn-outline-primary">
              <i class="bi bi-download"></i>
            </a>
          </div>
        `;
        
        // Add to the top of the list
        recentReports.insertBefore(reportItem, recentReports.firstChild);
        
        // Limit to 5 recent reports
        if (recentReports.children.length > 5) {
          recentReports.removeChild(recentReports.lastChild);
        }
      }
      
      // Initialize with default date values
      const today = new Date();
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(today.getFullYear() - 1);
      
      document.getElementById('startDate').value = oneYearAgo.toISOString().split('T')[0];
      document.getElementById('endDate').value = today.toISOString().split('T')[0];
    });
  </script>
</body>
</html>