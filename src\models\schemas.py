"""
Pydantic models for API request/response schemas
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator


class ConfigSchema(BaseModel):
    """Configuration schema for SEO analysis requests"""
    domain_property: str = Field(..., description="Domain property in GSC (e.g., https://example.com/)")
    ga_property_id: str = Field(..., description="Google Analytics property ID")
    service_account_file: str = Field(..., description="Path to Google service account JSON file")
    homepage: Optional[str] = Field(None, description="Homepage URL (defaults to domain_property)")
    start_date: Optional[str] = Field(None, description="Start date for data (YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="End date for data (YYYY-MM-DD)")
    website_urls: Optional[List[str]] = Field(None, description="Specific URLs to crawl (optional)")
    wp_api_key: Optional[str] = Field(None, description="WordPress API key if available")
    supabase_url: Optional[str] = Field(None, description="Supabase URL")
    supabase_key: Optional[str] = Field(None, description="Supabase API key")
    generate_excel: Optional[bool] = Field(True, description="Whether to generate Excel report")

    @validator('start_date', 'end_date')
    def validate_date_format(cls, v):
        if v is not None:
            from src.config.settings import validate_date_format
            if not validate_date_format(v):
                raise ValueError('Date must be in YYYY-MM-DD format')
        return v


class ExcelRequestSchema(BaseModel):
    """Schema for Excel report generation requests"""
    domain: str = Field(..., description="Domain to generate report for")
    date: Optional[str] = Field(None, description="Specific snapshot date (YYYY-MM-DD)")
    supabase_url: str = Field(..., description="Supabase URL")
    supabase_key: str = Field(..., description="Supabase API key")

    @validator('date')
    def validate_date_format(cls, v):
        if v is not None:
            from src.config.settings import validate_date_format
            if not validate_date_format(v):
                raise ValueError('Date must be in YYYY-MM-DD format')
        return v


class TaskResponse(BaseModel):
    """Response schema for task creation"""
    task_id: str
    status: str
    message: str


class TaskStatus(BaseModel):
    """Schema for task status responses"""
    status: str
    progress: Optional[int] = None
    message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class CrawlResult(BaseModel):
    """Schema for crawl results"""
    url: str
    title: str
    description: str
    h1: str
    text: str
    raw_html: Optional[str] = None


class InternalLink(BaseModel):
    """Schema for internal link data"""
    url: str = Field(alias="URL")
    target_hyperlink: str = Field(alias="Target Hyperlink")
    anchor_text: str = Field(alias="Anchor Text")
    link_type: str = Field(alias="Link Type")
    url_topic: str = Field(alias="URL Topic")
    target_title: str = Field(alias="Target Title")
    relevance_score: Optional[float] = Field(alias="Relevance Score")

    class Config:
        allow_population_by_field_name = True
