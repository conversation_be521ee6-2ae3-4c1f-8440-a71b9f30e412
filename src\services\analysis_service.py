"""
Main SEO analysis service - orchestrates the entire analysis process
"""
import os
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse

from src.core.crawler import WebCrawler
from src.core.google_apis import GoogleSearchConsoleClient, GoogleAnalyticsClient
from src.core.wordpress import WordPressAPIClient
from src.database.supabase_client import SupabaseClient, SUPABASE_AVAILABLE
from src.services.link_analysis_service import LinkAnalysisService
from src.services.report_service import ReportService
from src.utils.file_utils import get_output_directory, save_urls_list, create_temp_json_file, cleanup_temp_file
from src.utils.logging import get_logger
from src.config.settings import settings

logger = get_logger(__name__)


class SEOAnalysisService:
    """Main service for orchestrating SEO analysis"""
    
    def __init__(self):
        self.crawler = WebCrawler()
        self.link_analyzer = LinkAnalysisService()
        self.report_service = ReportService()
    
    async def run_analysis(self, config: Dict[str, Any], task_id: str, 
                          progress_callback: Optional[callable] = None) -> Dict[str, Any]:
        """
        Run complete SEO analysis
        
        Args:
            config: Analysis configuration
            task_id: Unique task identifier
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dict containing analysis results
        """
        try:
            # Initialize progress tracking
            if progress_callback:
                progress_callback(task_id, 0, "Starting analysis...")
            
            # Extract configuration
            domain_property = config['domain_property']
            ga_property_id = config['ga_property_id']
            service_account_file = config.get('service_account_file')
            homepage = config.get('homepage') or domain_property
            start_date = config.get('start_date')
            end_date = config.get('end_date')
            website_urls = config.get('website_urls', [])
            wp_api_key = config.get('wp_api_key')
            generate_excel = config.get('generate_excel', True)
            
            # Set default dates if not provided
            if not end_date:
                end_date = datetime.now().strftime('%Y-%m-%d')
            if not start_date:
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
            
            # Create output directory
            output_dir = get_output_directory(domain_property)
            
            if progress_callback:
                progress_callback(task_id, 10, "Initializing Google API clients...")
            
            # Initialize Google API clients
            gsc_client = GoogleSearchConsoleClient(service_account_file)
            ga_client = GoogleAnalyticsClient(service_account_file)
            
            if progress_callback:
                progress_callback(task_id, 20, "Discovering website URLs...")
            
            # Get website URLs
            if not website_urls:
                website_urls = await self.crawler.discover_urls_from_homepage(homepage)
            
            save_urls_list(website_urls, output_dir)
            
            # Try WordPress API if available
            wp_data = None
            crawl_results = []
            
            if wp_api_key:
                if progress_callback:
                    progress_callback(task_id, 25, "Checking WordPress API...")
                
                wp_client = WordPressAPIClient(wp_api_key)
                wp_api_url = wp_client.detect_wp_api(domain_property)
                
                if wp_api_url:
                    wp_data = wp_client.fetch_data(wp_api_url)
                    if wp_data and 'pages_data' in wp_data:
                        crawl_results = wp_data['pages_data']
            
            if progress_callback:
                progress_callback(task_id, 35, f"Crawling {len(website_urls)} URLs...")
            
            # Crawl website if no WordPress data or limited data
            if not crawl_results or len(crawl_results) <= 1:
                crawl_results = self.crawler.crawl_site(website_urls, output_dir)
            
            if progress_callback:
                progress_callback(task_id, 50, "Fetching Google Search Console data...")
            
            # Fetch GSC data
            keywords_df = gsc_client.get_data_by_month(
                domain_property, start_date, end_date,
                dimensions=['query', 'page'],
                metrics=['clicks', 'impressions', 'ctr', 'position']
            )
            
            hist_traffic_df = gsc_client.get_data_by_month(
                domain_property, start_date, end_date,
                dimensions=['page'],
                metrics=['clicks', 'impressions', 'ctr', 'position']
            )
            
            if progress_callback:
                progress_callback(task_id, 65, "Fetching Google Analytics data...")
            
            # Fetch GA data
            ga_df = ga_client.get_data_by_month(
                ga_property_id, start_date, end_date,
                dimensions=['pagePath'],
                metrics=['sessions', 'pageviews', 'bounceRate', 'avgSessionDuration']
            )
            
            if progress_callback:
                progress_callback(task_id, 75, "Analyzing internal links...")
            
            # Analyze internal links
            internal_links_df = self.link_analyzer.build_internal_links_sheet(
                crawl_results, keywords_df, wp_data
            )
            
            if progress_callback:
                progress_callback(task_id, 85, "Generating reports...")
            
            # Generate Excel report
            excel_path = None
            if generate_excel:
                excel_path = self.report_service.generate_excel_report(
                    crawl_results, keywords_df, hist_traffic_df, 
                    internal_links_df, ga_df, output_dir
                )
            
            # Save to Supabase if configured
            if SUPABASE_AVAILABLE and config.get('supabase_url') and config.get('supabase_key'):
                if progress_callback:
                    progress_callback(task_id, 90, "Saving data to Supabase...")
                
                domain = urlparse(domain_property).netloc
                supabase_client = SupabaseClient(
                    url=config['supabase_url'],
                    key=config['supabase_key'],
                    domain=domain
                )
                
                # Convert crawl results to DataFrame for Supabase
                import pandas as pd
                data_df = pd.DataFrame([result.dict() for result in crawl_results])
                
                supabase_client.save_pages_data(data_df)
                supabase_client.save_gsc_keywords(keywords_df)
                supabase_client.save_gsc_traffic(hist_traffic_df)
                supabase_client.save_internal_links(internal_links_df)
                
                if not ga_df.empty:
                    supabase_client.save_ga_data(ga_df)
            
            if progress_callback:
                progress_callback(task_id, 100, "Analysis completed successfully!")
            
            # Return results
            result = {
                'domain': urlparse(domain_property).netloc,
                'pages_analyzed': len(crawl_results),
                'keywords_found': len(keywords_df),
                'internal_links': len(internal_links_df),
                'output_directory': output_dir
            }
            
            if excel_path:
                result['excel_report'] = excel_path
            
            return result
            
        except Exception as e:
            logger.exception(f"Error in SEO analysis for task {task_id}")
            if progress_callback:
                progress_callback(task_id, -1, f"Analysis failed: {str(e)}")
            raise
